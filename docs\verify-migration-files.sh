#!/bin/bash

# RAGFlow 离线迁移文件验证脚本
# 用于验证所有迁移相关文件是否正确创建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查文件是否存在
check_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        local size=$(du -h "$file" | cut -f1)
        log_success "$description 存在 (大小: $size)"
        return 0
    else
        log_error "$description 不存在: $file"
        return 1
    fi
}

# 检查文件内容
check_file_content() {
    local file="$1"
    local pattern="$2"
    local description="$3"
    
    if [ -f "$file" ] && grep -q "$pattern" "$file"; then
        log_success "$description 内容验证通过"
        return 0
    else
        log_warning "$description 内容可能有问题"
        return 1
    fi
}

# 主验证函数
main() {
    log_info "RAGFlow 离线迁移文件验证"
    log_info "=========================="
    
    local error_count=0
    
    # 检查文档文件
    log_info "检查文档文件..."
    
    if ! check_file "docs/docker-offline-migration.md" "完整迁移文档"; then
        ((error_count++))
    fi
    
    if ! check_file "docs/quick-migration-guide.md" "快速迁移指南"; then
        ((error_count++))
    fi
    
    if ! check_file "docs/offline-migration-index.md" "迁移索引文档"; then
        ((error_count++))
    fi
    
    # 检查脚本文件
    log_info "检查脚本文件..."
    
    if ! check_file "docs/export-ragflow.sh" "自动导出脚本"; then
        ((error_count++))
    fi
    
    if ! check_file "docs/install-docker-ubuntu.sh" "Docker安装脚本"; then
        ((error_count++))
    fi
    
    # 检查脚本内容
    log_info "验证脚本内容..."
    
    if ! check_file_content "docs/export-ragflow.sh" "#!/bin/bash" "导出脚本Shebang"; then
        ((error_count++))
    fi
    
    if ! check_file_content "docs/install-docker-ubuntu.sh" "#!/bin/bash" "安装脚本Shebang"; then
        ((error_count++))
    fi
    
    if ! check_file_content "docs/export-ragflow.sh" "docker save" "导出脚本Docker命令"; then
        ((error_count++))
    fi
    
    if ! check_file_content "docs/install-docker-ubuntu.sh" "apt-get install" "安装脚本APT命令"; then
        ((error_count++))
    fi
    
    # 检查文档内容
    log_info "验证文档内容..."
    
    if ! check_file_content "docs/docker-offline-migration.md" "RAGFlow Docker 离线迁移操作指南" "迁移文档标题"; then
        ((error_count++))
    fi
    
    if ! check_file_content "docs/quick-migration-guide.md" "一键导出" "快速指南关键词"; then
        ((error_count++))
    fi
    
    # 检查文件权限（仅在Linux/Unix系统上）
    if [ "$(uname)" != "MINGW64_NT"* ] && [ "$(uname)" != "MSYS_NT"* ]; then
        log_info "检查脚本执行权限..."
        
        if [ -x "docs/export-ragflow.sh" ]; then
            log_success "导出脚本有执行权限"
        else
            log_warning "导出脚本没有执行权限，请运行: chmod +x docs/export-ragflow.sh"
        fi
        
        if [ -x "docs/install-docker-ubuntu.sh" ]; then
            log_success "安装脚本有执行权限"
        else
            log_warning "安装脚本没有执行权限，请运行: chmod +x docs/install-docker-ubuntu.sh"
        fi
    fi
    
    # 统计结果
    echo
    if [ $error_count -eq 0 ]; then
        log_success "所有文件验证通过！"
        echo
        log_info "文件列表："
        echo "📖 docs/docker-offline-migration.md - 完整迁移操作指南"
        echo "🚀 docs/quick-migration-guide.md - 快速迁移指南"
        echo "📋 docs/offline-migration-index.md - 迁移文档索引"
        echo "🔧 docs/export-ragflow.sh - 自动导出脚本"
        echo "⚙️ docs/install-docker-ubuntu.sh - Docker安装脚本"
        echo
        log_info "使用方法："
        echo "1. 在源服务器运行: ./docs/export-ragflow.sh"
        echo "2. 在目标服务器运行: ./docs/install-docker-ubuntu.sh"
        echo "3. 参考文档进行完整迁移操作"
        
    else
        log_error "发现 $error_count 个问题，请检查上述错误信息"
        exit 1
    fi
}

# 运行验证
main "$@"
