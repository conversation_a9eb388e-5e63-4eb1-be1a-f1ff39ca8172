#!/bin/bash

# Ubuntu Docker 和 Docker Compose 自动安装脚本
# 适用于 Ubuntu 18.04, 20.04, 22.04, 24.04

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log_warning "检测到root用户，建议使用普通用户运行此脚本"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查Ubuntu版本
check_ubuntu_version() {
    if [ ! -f /etc/os-release ]; then
        log_error "无法检测操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    if [ "$ID" != "ubuntu" ]; then
        log_error "此脚本仅支持Ubuntu系统，当前系统: $ID"
        exit 1
    fi
    
    log_info "检测到Ubuntu版本: $VERSION"
    
    # 检查支持的版本
    case $VERSION_ID in
        "18.04"|"20.04"|"22.04"|"24.04")
            log_success "支持的Ubuntu版本"
            ;;
        *)
            log_warning "未测试的Ubuntu版本: $VERSION_ID"
            read -p "是否继续安装？(y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
            ;;
    esac
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    if ! ping -c 1 google.com &> /dev/null && ! ping -c 1 baidu.com &> /dev/null; then
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    log_success "网络连接正常"
}

# 检查是否已安装Docker
check_existing_docker() {
    if command -v docker &> /dev/null; then
        local docker_version=$(docker --version)
        log_warning "检测到已安装的Docker: $docker_version"
        read -p "是否重新安装？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过Docker安装"
            return 1
        fi
        
        log_info "卸载现有Docker..."
        sudo apt-get remove -y docker docker-engine docker.io containerd runc docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin 2>/dev/null || true
    fi
    return 0
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    sudo apt-get update
    sudo apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release \
        software-properties-common
    log_success "系统包更新完成"
}

# 安装Docker
install_docker() {
    if ! check_existing_docker; then
        return 0
    fi
    
    log_info "安装Docker..."
    
    # 添加Docker官方GPG密钥
    log_info "添加Docker GPG密钥..."
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    log_info "添加Docker仓库..."
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 更新包索引
    sudo apt-get update
    
    # 安装Docker CE
    log_info "安装Docker CE..."
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # 启动Docker服务
    log_info "启动Docker服务..."
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log_success "Docker安装完成"
}

# 安装Docker Compose (standalone)
install_docker_compose() {
    log_info "安装Docker Compose..."
    
    # 获取最新版本
    local latest_version=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep -Po '"tag_name": "\K.*?(?=")')
    if [ -z "$latest_version" ]; then
        latest_version="v2.24.0"  # 备用版本
        log_warning "无法获取最新版本，使用备用版本: $latest_version"
    fi
    
    log_info "下载Docker Compose $latest_version..."
    sudo curl -L "https://github.com/docker/compose/releases/download/$latest_version/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 设置执行权限
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建符号链接（可选）
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose 2>/dev/null || true
    
    log_success "Docker Compose安装完成"
}

# 配置用户权限
configure_user_permissions() {
    log_info "配置用户权限..."
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    log_success "用户权限配置完成"
    log_warning "请注销并重新登录，或运行 'newgrp docker' 以应用权限更改"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 验证Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker安装失败"
        return 1
    fi
    
    local docker_version=$(docker --version)
    log_success "Docker版本: $docker_version"
    
    # 验证Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose安装失败"
        return 1
    fi
    
    local compose_version=$(docker-compose --version)
    log_success "Docker Compose版本: $compose_version"
    
    # 测试Docker运行
    log_info "测试Docker运行..."
    if sudo docker run --rm hello-world &> /dev/null; then
        log_success "Docker运行测试通过"
    else
        log_warning "Docker运行测试失败，可能需要重启系统"
    fi
    
    return 0
}

# 显示后续步骤
show_next_steps() {
    echo
    log_success "Docker和Docker Compose安装完成！"
    echo
    log_info "后续步骤："
    echo "1. 注销并重新登录，或运行: newgrp docker"
    echo "2. 验证安装: docker --version && docker-compose --version"
    echo "3. 测试运行: docker run hello-world"
    echo "4. 现在可以导入RAGFlow迁移包了"
    echo
    log_info "RAGFlow导入命令示例："
    echo "tar xzf ragflow-migration-*.tar.gz"
    echo "cd ragflow-export-*/"
    echo "./import.sh"
    echo "cd config"
    echo "docker-compose up -d"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    sudo apt-get autoremove -y
    sudo apt-get autoclean
}

# 主函数
main() {
    log_info "Ubuntu Docker 自动安装脚本"
    log_info "============================="
    
    # 环境检查
    check_root
    check_ubuntu_version
    check_network
    
    # 确认安装
    echo
    log_info "即将安装以下组件："
    echo "- Docker CE (最新稳定版)"
    echo "- Docker Compose (最新版)"
    echo "- 配置用户权限"
    echo
    read -p "是否继续安装？(Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi
    
    # 执行安装
    update_system
    install_docker
    install_docker_compose
    configure_user_permissions
    
    # 验证和清理
    if verify_installation; then
        cleanup
        show_next_steps
    else
        log_error "安装验证失败，请检查错误信息"
        exit 1
    fi
}

# 错误处理
trap 'log_error "脚本执行失败，请检查错误信息"; exit 1' ERR

# 运行主函数
main "$@"
