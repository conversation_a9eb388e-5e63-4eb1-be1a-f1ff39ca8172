# RAGFlow Docker 离线迁移完整指南

本指南提供完整的RAGFlow Docker服务离线迁移解决方案，帮助您将运行中的RAGFlow服务迁移到另一台离线的Ubuntu服务器。

## 📋 文档概览

| 文档 | 描述 | 使用场景 |
|------|------|----------|
| [🚀 快速迁移指南](quick-migration-guide.md) | 一键式迁移操作，简单快速 | 标准环境，快速迁移 |
| [📖 完整迁移文档](docker-offline-migration.md) | 详细的迁移步骤和说明 | 复杂环境，定制需求 |
| [🔧 自动导出脚本](export-ragflow.sh) | 源服务器自动导出工具 | 自动化批量导出 |
| [⚙️ Docker安装脚本](install-docker-ubuntu.sh) | Ubuntu Docker环境自动安装 | 目标服务器环境准备 |

## 🎯 迁移流程

```mermaid
graph LR
    A[源服务器] --> B[导出RAGFlow]
    B --> C[生成迁移包]
    C --> D[传输到目标服务器]
    D --> E[安装Docker环境]
    E --> F[导入RAGFlow]
    F --> G[启动服务]
    G --> H[验证功能]
```

## ⚡ 快速开始

### 第一步：源服务器导出

```bash
# 进入RAGFlow docker目录
cd /path/to/ragflow/docker

# 下载并运行自动导出脚本
curl -O https://raw.githubusercontent.com/infiniflow/ragflow/main/docs/export-ragflow.sh
chmod +x export-ragflow.sh
./export-ragflow.sh
```

### 第二步：传输迁移包

```bash
# 网络传输（如果可达）
scp ragflow-migration-*.tar.gz user@target-server:/home/<USER>/

# 或使用物理介质传输
```

### 第三步：目标服务器导入

```bash
# 安装Docker环境（如果未安装）
curl -O https://raw.githubusercontent.com/infiniflow/ragflow/main/docs/install-docker-ubuntu.sh
chmod +x install-docker-ubuntu.sh
./install-docker-ubuntu.sh

# 解压并导入RAGFlow
tar xzf ragflow-migration-*.tar.gz
cd ragflow-export-*/
./import.sh

# 启动服务
cd config
docker-compose up -d
```

### 第四步：验证服务

```bash
# 检查容器状态
docker-compose ps

# 检查服务健康状态
curl http://localhost:9380/v1/system/status

# 访问Web界面
echo "访问: http://your-server-ip:9380"
```

## 🔧 支持的配置

### 文档引擎
- ✅ **Elasticsearch** (默认)
- ✅ **OpenSearch**
- ✅ **Infinity**

### 可选组件
- ✅ **Sandbox代码执行环境**
- ✅ **自定义镜像配置**
- ✅ **多种存储后端**

### 系统要求

#### 源服务器
- 运行中的RAGFlow Docker服务
- Docker 20.10+ 和 Docker Compose 2.0+
- 可用磁盘空间 ≥ 10GB

#### 目标服务器
- Ubuntu 18.04/20.04/22.04/24.04
- 可用磁盘空间 ≥ 20GB
- 内存 ≥ 8GB（推荐16GB+）

## 📊 迁移时间估算

| 数据量 | 导出时间 | 传输时间 | 导入时间 | 总时间 |
|--------|----------|----------|----------|--------|
| < 1GB | 5-10分钟 | 取决于网络 | 5-10分钟 | 15-30分钟 |
| 1-5GB | 10-20分钟 | 取决于网络 | 10-15分钟 | 30-60分钟 |
| 5-20GB | 20-40分钟 | 取决于网络 | 15-30分钟 | 1-2小时 |
| > 20GB | 40分钟+ | 取决于网络 | 30分钟+ | 2小时+ |

## ⚠️ 重要提醒

### 迁移前检查
- [ ] 确保源服务器RAGFlow正常运行
- [ ] 检查磁盘空间是否充足
- [ ] 备份重要数据（可选但推荐）
- [ ] 记录当前配置信息

### 迁移中注意
- [ ] 确保导出过程不被中断
- [ ] 验证迁移包完整性
- [ ] 安全传输迁移包

### 迁移后验证
- [ ] 检查所有容器状态
- [ ] 验证数据完整性
- [ ] 测试核心功能
- [ ] 检查日志无错误

## 🆘 故障排除

### 常见问题

1. **导出失败**
   - 检查磁盘空间
   - 确认Docker权限
   - 查看详细错误日志

2. **导入失败**
   - 验证镜像完整性
   - 检查Docker版本兼容性
   - 确认卷权限设置

3. **服务启动失败**
   - 检查端口占用
   - 验证配置文件
   - 查看容器日志

### 获取帮助

- 📖 [详细故障排除指南](docker-offline-migration.md#故障排除)
- 🌐 [RAGFlow官方文档](https://ragflow.io/docs)
- 💬 [GitHub Issues](https://github.com/infiniflow/ragflow/issues)
- 📧 [社区支持](https://github.com/infiniflow/ragflow/discussions)

## 🔄 版本兼容性

| RAGFlow版本 | 支持状态 | 备注 |
|-------------|----------|------|
| v0.20.x | ✅ 完全支持 | 推荐版本 |
| v0.19.x | ✅ 支持 | 需要注意配置差异 |
| v0.18.x | ⚠️ 部分支持 | 可能需要手动调整 |
| < v0.18 | ❌ 不支持 | 建议先升级 |

## 📈 性能优化建议

### 导出优化
- 使用SSD存储提高I/O性能
- 在低负载时段进行导出
- 考虑并行导出多个卷

### 传输优化
- 使用压缩传输减少数据量
- 选择高带宽网络连接
- 考虑分片传输大文件

### 导入优化
- 确保目标服务器资源充足
- 使用高性能存储
- 预先拉取基础镜像

---

**最后更新**: 2025-01-26  
**适用版本**: RAGFlow v0.20.3+  
**维护者**: RAGFlow团队
