# RAGFlow Docker 离线迁移操作指南

本文档详细介绍如何将运行中的RAGFlow Docker服务导出并迁移到另一台离线的Ubuntu服务器。

## 目录
- [前提条件](#前提条件)
- [源服务器操作（导出）](#源服务器操作导出)
- [目标服务器操作（导入）](#目标服务器操作导入)
- [验证和启动](#验证和启动)
- [故障排除](#故障排除)

## 前提条件

### 源服务器要求
- 已安装Docker和Docker Compose
- RAGFlow服务正常运行
- 足够的磁盘空间用于导出镜像和数据

### 目标服务器要求
- Ubuntu系统
- 已安装Docker和Docker Compose
- 足够的磁盘空间用于导入镜像和数据

## 源服务器操作（导出）

### 1. 停止RAGFlow服务

```bash
cd /path/to/ragflow/docker
docker-compose down
```

### 2. 查看当前使用的Docker镜像

```bash
# 查看RAGFlow相关镜像
docker images | grep -E "(ragflow|elasticsearch|mysql|minio|valkey|infinity|opensearch|sandbox)"
```

### 3. 导出Docker镜像

根据您的配置，导出相应的镜像：

```bash
# 创建导出目录
mkdir -p ragflow-export/images

# 导出RAGFlow主镜像
docker save infiniflow/ragflow:v0.20.3-slim -o ragflow-export/images/ragflow.tar

# 导出基础服务镜像
docker save mysql:8.0.39 -o ragflow-export/images/mysql.tar
docker save quay.io/minio/minio:RELEASE.2025-06-13T11-33-47Z -o ragflow-export/images/minio.tar
docker save valkey/valkey:8 -o ragflow-export/images/valkey.tar

# 根据DOC_ENGINE配置导出相应的搜索引擎镜像
# 如果使用Elasticsearch
docker save elasticsearch:8.11.3 -o ragflow-export/images/elasticsearch.tar

# 如果使用OpenSearch
# docker save hub.icert.top/opensearchproject/opensearch:2.19.1 -o ragflow-export/images/opensearch.tar

# 如果使用Infinity
# docker save infiniflow/infinity:v0.6.0-dev5 -o ragflow-export/images/infinity.tar

# 如果启用了Sandbox功能
# docker save infiniflow/sandbox-executor-manager:latest -o ragflow-export/images/sandbox-executor-manager.tar
# docker save infiniflow/sandbox-base-python:latest -o ragflow-export/images/sandbox-base-python.tar
# docker save infiniflow/sandbox-base-nodejs:latest -o ragflow-export/images/sandbox-base-nodejs.tar
```

### 4. 导出Docker卷数据

```bash
# 创建数据导出目录
mkdir -p ragflow-export/volumes

# 导出MySQL数据
docker run --rm -v docker_mysql_data:/data -v $(pwd)/ragflow-export/volumes:/backup alpine tar czf /backup/mysql_data.tar.gz -C /data .

# 导出MinIO数据
docker run --rm -v docker_minio_data:/data -v $(pwd)/ragflow-export/volumes:/backup alpine tar czf /backup/minio_data.tar.gz -C /data .

# 导出Redis数据
docker run --rm -v docker_redis_data:/data -v $(pwd)/ragflow-export/volumes:/backup alpine tar czf /backup/redis_data.tar.gz -C /data .

# 根据DOC_ENGINE导出相应的搜索引擎数据
# 如果使用Elasticsearch
docker run --rm -v docker_esdata01:/data -v $(pwd)/ragflow-export/volumes:/backup alpine tar czf /backup/esdata01.tar.gz -C /data .

# 如果使用OpenSearch
# docker run --rm -v docker_osdata01:/data -v $(pwd)/ragflow-export/volumes:/backup alpine tar czf /backup/osdata01.tar.gz -C /data .

# 如果使用Infinity
# docker run --rm -v docker_infinity_data:/data -v $(pwd)/ragflow-export/volumes:/backup alpine tar czf /backup/infinity_data.tar.gz -C /data .
```

### 5. 复制配置文件

```bash
# 复制RAGFlow配置文件
cp -r . ragflow-export/ragflow-config/
```

### 6. 创建迁移脚本

```bash
cat > ragflow-export/import.sh << 'EOF'
#!/bin/bash

echo "开始导入RAGFlow Docker镜像和数据..."

# 导入Docker镜像
echo "导入Docker镜像..."
docker load -i images/ragflow.tar
docker load -i images/mysql.tar
docker load -i images/minio.tar
docker load -i images/valkey.tar

# 根据需要导入搜索引擎镜像
if [ -f "images/elasticsearch.tar" ]; then
    docker load -i images/elasticsearch.tar
fi

if [ -f "images/opensearch.tar" ]; then
    docker load -i images/opensearch.tar
fi

if [ -f "images/infinity.tar" ]; then
    docker load -i images/infinity.tar
fi

# 导入Sandbox镜像（如果存在）
if [ -f "images/sandbox-executor-manager.tar" ]; then
    docker load -i images/sandbox-executor-manager.tar
    docker load -i images/sandbox-base-python.tar
    docker load -i images/sandbox-base-nodejs.tar
fi

echo "Docker镜像导入完成"

# 创建Docker卷
echo "创建Docker卷..."
docker volume create docker_mysql_data
docker volume create docker_minio_data
docker volume create docker_redis_data

# 根据DOC_ENGINE创建相应的搜索引擎卷
if [ -f "volumes/esdata01.tar.gz" ]; then
    docker volume create docker_esdata01
fi

if [ -f "volumes/osdata01.tar.gz" ]; then
    docker volume create docker_osdata01
fi

if [ -f "volumes/infinity_data.tar.gz" ]; then
    docker volume create docker_infinity_data
fi

# 导入卷数据
echo "导入卷数据..."
docker run --rm -v docker_mysql_data:/data -v $(pwd)/volumes:/backup alpine tar xzf /backup/mysql_data.tar.gz -C /data
docker run --rm -v docker_minio_data:/data -v $(pwd)/volumes:/backup alpine tar xzf /backup/minio_data.tar.gz -C /data
docker run --rm -v docker_redis_data:/data -v $(pwd)/volumes:/backup alpine tar xzf /backup/redis_data.tar.gz -C /data

# 根据需要导入搜索引擎数据
if [ -f "volumes/esdata01.tar.gz" ]; then
    docker run --rm -v docker_esdata01:/data -v $(pwd)/volumes:/backup alpine tar xzf /backup/esdata01.tar.gz -C /data
fi

if [ -f "volumes/osdata01.tar.gz" ]; then
    docker run --rm -v docker_osdata01:/data -v $(pwd)/volumes:/backup alpine tar xzf /backup/osdata01.tar.gz -C /data
fi

if [ -f "volumes/infinity_data.tar.gz" ]; then
    docker run --rm -v docker_infinity_data:/data -v $(pwd)/volumes:/backup alpine tar xzf /backup/infinity_data.tar.gz -C /data
fi

echo "数据导入完成"
echo "请进入ragflow-config目录并运行: docker-compose up -d"
EOF

chmod +x ragflow-export/import.sh
```

### 7. 打包导出文件

```bash
# 压缩整个导出目录
tar czf ragflow-migration-$(date +%Y%m%d-%H%M%S).tar.gz ragflow-export/

echo "导出完成！迁移包: ragflow-migration-$(date +%Y%m%d-%H%M%S).tar.gz"
```

## 目标服务器操作（导入）

### 1. 安装Docker和Docker Compose

```bash
# 更新系统
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 将当前用户添加到docker组
sudo usermod -aG docker $USER
newgrp docker
```

### 2. 传输迁移包

将迁移包传输到目标服务器：

```bash
# 使用scp传输（如果网络可达）
scp ragflow-migration-*.tar.gz user@target-server:/home/<USER>/

# 或使用USB/移动存储设备物理传输
```

### 3. 解压并导入

```bash
# 解压迁移包
tar xzf ragflow-migration-*.tar.gz

# 进入导出目录
cd ragflow-export

# 运行导入脚本
./import.sh
```

## 验证和启动

### 1. 验证镜像导入

```bash
# 检查镜像是否成功导入
docker images | grep -E "(ragflow|elasticsearch|mysql|minio|valkey|infinity|opensearch|sandbox)"
```

### 2. 验证卷数据

```bash
# 检查卷是否创建
docker volume ls | grep docker_
```

### 3. 启动RAGFlow服务

```bash
# 进入配置目录
cd ragflow-config

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f ragflow
```

### 4. 验证服务可用性

```bash
# 等待服务启动完成（通常需要几分钟）
sleep 60

# 检查RAGFlow API是否可用
curl -f http://localhost:9380/v1/system/status || echo "服务尚未就绪，请稍等..."

# 访问Web界面
echo "请在浏览器中访问: http://your-server-ip:9380"
```

## 故障排除

### 常见问题

1. **镜像导入失败**
   ```bash
   # 检查镜像文件完整性
   docker load -i images/ragflow.tar
   ```

2. **卷数据导入失败**
   ```bash
   # 手动检查卷数据
   docker run --rm -v docker_mysql_data:/data alpine ls -la /data
   ```

3. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs --tail=100 ragflow
   
   # 检查端口占用
   netstat -tlnp | grep -E "(9380|3306|9000|6379)"
   ```

4. **权限问题**
   ```bash
   # 修复卷权限
   docker run --rm -v docker_mysql_data:/data alpine chown -R 999:999 /data
   docker run --rm -v docker_minio_data:/data alpine chown -R 1000:1000 /data
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs ragflow
docker-compose logs mysql
docker-compose logs minio
```

### 重新启动服务

```bash
# 停止所有服务
docker-compose down

# 重新启动
docker-compose up -d
```

## 注意事项

1. **数据一致性**: 确保在导出前停止所有写入操作
2. **版本兼容**: 确保目标服务器的Docker版本与源服务器兼容
3. **磁盘空间**: 确保目标服务器有足够的磁盘空间
4. **网络配置**: 根据需要调整防火墙和端口配置
5. **备份**: 在导入前建议备份目标服务器的现有数据

## 自动化脚本

为了简化操作，您可以将上述步骤整合到自动化脚本中，详细的自动化脚本请参考项目中的其他文档。
