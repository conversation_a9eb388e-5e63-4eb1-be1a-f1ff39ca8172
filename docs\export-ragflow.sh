#!/bin/bash

# RAGFlow Docker 自动导出脚本
# 用于将运行中的RAGFlow服务导出为离线迁移包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查Docker是否运行
check_docker() {
    if ! docker info &> /dev/null; then
        log_error "Docker未运行或无权限访问，请检查Docker状态"
        exit 1
    fi
}

# 获取当前时间戳
get_timestamp() {
    date +%Y%m%d-%H%M%S
}

# 检查磁盘空间
check_disk_space() {
    local required_space=10  # GB
    local available_space=$(df . | awk 'NR==2 {print int($4/1024/1024)}')
    
    if [ $available_space -lt $required_space ]; then
        log_warning "可用磁盘空间不足 ${required_space}GB，当前可用: ${available_space}GB"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 读取.env文件
read_env_file() {
    if [ -f ".env" ]; then
        log_info "读取.env配置文件..."
        source .env
        DOC_ENGINE=${DOC_ENGINE:-elasticsearch}
        RAGFLOW_IMAGE=${RAGFLOW_IMAGE:-infiniflow/ragflow:v0.20.3-slim}
        log_info "文档引擎: $DOC_ENGINE"
        log_info "RAGFlow镜像: $RAGFLOW_IMAGE"
    else
        log_error ".env文件未找到，请确保在RAGFlow docker目录中运行此脚本"
        exit 1
    fi
}

# 停止RAGFlow服务
stop_ragflow() {
    log_info "停止RAGFlow服务..."
    if docker-compose ps | grep -q "Up"; then
        docker-compose down
        log_success "RAGFlow服务已停止"
    else
        log_info "RAGFlow服务未运行"
    fi
}

# 创建导出目录
create_export_dir() {
    EXPORT_DIR="ragflow-export-$(get_timestamp)"
    log_info "创建导出目录: $EXPORT_DIR"
    mkdir -p "$EXPORT_DIR"/{images,volumes,config}
}

# 导出Docker镜像
export_images() {
    log_info "开始导出Docker镜像..."
    
    # 基础镜像列表
    local images=(
        "$RAGFLOW_IMAGE:ragflow.tar"
        "mysql:8.0.39:mysql.tar"
        "quay.io/minio/minio:RELEASE.2025-06-13T11-33-47Z:minio.tar"
        "valkey/valkey:8:valkey.tar"
    )
    
    # 根据DOC_ENGINE添加相应镜像
    case $DOC_ENGINE in
        "elasticsearch")
            images+=("elasticsearch:8.11.3:elasticsearch.tar")
            ;;
        "opensearch")
            images+=("hub.icert.top/opensearchproject/opensearch:2.19.1:opensearch.tar")
            ;;
        "infinity")
            images+=("infiniflow/infinity:v0.6.0-dev5:infinity.tar")
            ;;
    esac
    
    # 检查是否启用了Sandbox
    if [ "$SANDBOX_ENABLED" = "1" ]; then
        images+=(
            "infiniflow/sandbox-executor-manager:latest:sandbox-executor-manager.tar"
            "infiniflow/sandbox-base-python:latest:sandbox-base-python.tar"
            "infiniflow/sandbox-base-nodejs:latest:sandbox-base-nodejs.tar"
        )
    fi
    
    # 导出镜像
    for image_info in "${images[@]}"; do
        IFS=':' read -r image_name filename <<< "$image_info"
        if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^$image_name$"; then
            log_info "导出镜像: $image_name"
            docker save "$image_name" -o "$EXPORT_DIR/images/$filename"
            log_success "已导出: $filename"
        else
            log_warning "镜像不存在，跳过: $image_name"
        fi
    done
}

# 导出Docker卷数据
export_volumes() {
    log_info "开始导出Docker卷数据..."
    
    # 基础卷列表
    local volumes=(
        "docker_mysql_data:mysql_data.tar.gz"
        "docker_minio_data:minio_data.tar.gz"
        "docker_redis_data:redis_data.tar.gz"
    )
    
    # 根据DOC_ENGINE添加相应卷
    case $DOC_ENGINE in
        "elasticsearch")
            volumes+=("docker_esdata01:esdata01.tar.gz")
            ;;
        "opensearch")
            volumes+=("docker_osdata01:osdata01.tar.gz")
            ;;
        "infinity")
            volumes+=("docker_infinity_data:infinity_data.tar.gz")
            ;;
    esac
    
    # 导出卷数据
    for volume_info in "${volumes[@]}"; do
        IFS=':' read -r volume_name filename <<< "$volume_info"
        if docker volume ls --format "{{.Name}}" | grep -q "^$volume_name$"; then
            log_info "导出卷数据: $volume_name"
            docker run --rm \
                -v "$volume_name":/data \
                -v "$(pwd)/$EXPORT_DIR/volumes":/backup \
                alpine tar czf "/backup/$filename" -C /data .
            log_success "已导出: $filename"
        else
            log_warning "卷不存在，跳过: $volume_name"
        fi
    done
}

# 复制配置文件
copy_config() {
    log_info "复制配置文件..."
    cp -r . "$EXPORT_DIR/config/"
    
    # 排除不需要的文件
    rm -rf "$EXPORT_DIR/config/ragflow-logs" 2>/dev/null || true
    rm -rf "$EXPORT_DIR/config/ragflow-export-"* 2>/dev/null || true
    
    log_success "配置文件已复制"
}

# 创建导入脚本
create_import_script() {
    log_info "创建导入脚本..."
    
    cat > "$EXPORT_DIR/import.sh" << 'EOF'
#!/bin/bash

# RAGFlow Docker 自动导入脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker
if ! docker info &> /dev/null; then
    log_error "Docker未运行或无权限访问"
    exit 1
fi

log_info "开始导入RAGFlow..."

# 导入镜像
log_info "导入Docker镜像..."
for image_file in images/*.tar; do
    if [ -f "$image_file" ]; then
        log_info "导入: $(basename $image_file)"
        docker load -i "$image_file"
    fi
done

# 创建卷
log_info "创建Docker卷..."
for volume_file in volumes/*.tar.gz; do
    if [ -f "$volume_file" ]; then
        volume_name="docker_$(basename $volume_file .tar.gz)"
        log_info "创建卷: $volume_name"
        docker volume create "$volume_name" 2>/dev/null || true
        
        log_info "导入数据到: $volume_name"
        docker run --rm \
            -v "$volume_name":/data \
            -v "$(pwd)/volumes":/backup \
            alpine tar xzf "/backup/$(basename $volume_file)" -C /data
    fi
done

log_success "导入完成！"
log_info "请进入config目录并运行: docker-compose up -d"
EOF

    chmod +x "$EXPORT_DIR/import.sh"
    log_success "导入脚本已创建"
}

# 创建README文件
create_readme() {
    log_info "创建README文件..."
    
    cat > "$EXPORT_DIR/README.md" << EOF
# RAGFlow 离线迁移包

导出时间: $(date)
源系统: $(uname -a)
Docker版本: $(docker --version)
Docker Compose版本: $(docker-compose --version)

## 文件结构

- \`images/\`: Docker镜像文件
- \`volumes/\`: Docker卷数据
- \`config/\`: RAGFlow配置文件
- \`import.sh\`: 自动导入脚本

## 使用方法

1. 将此目录复制到目标服务器
2. 确保目标服务器已安装Docker和Docker Compose
3. 运行导入脚本: \`./import.sh\`
4. 进入config目录启动服务: \`cd config && docker-compose up -d\`

## 配置信息

- 文档引擎: $DOC_ENGINE
- RAGFlow镜像: $RAGFLOW_IMAGE
- Sandbox启用: ${SANDBOX_ENABLED:-0}

详细操作说明请参考: docs/docker-offline-migration.md
EOF

    log_success "README文件已创建"
}

# 打包导出文件
create_package() {
    log_info "打包导出文件..."
    
    PACKAGE_NAME="ragflow-migration-$(get_timestamp).tar.gz"
    tar czf "$PACKAGE_NAME" "$EXPORT_DIR/"
    
    # 计算文件大小
    local size=$(du -h "$PACKAGE_NAME" | cut -f1)
    
    log_success "导出包已创建: $PACKAGE_NAME (大小: $size)"
    
    # 清理临时目录
    read -p "是否删除临时导出目录？(Y/n): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        rm -rf "$EXPORT_DIR"
        log_info "临时目录已清理"
    fi
}

# 主函数
main() {
    log_info "RAGFlow Docker 导出工具"
    log_info "========================"
    
    # 检查环境
    check_command docker
    check_command docker-compose
    check_docker
    check_disk_space
    
    # 读取配置
    read_env_file
    
    # 确认操作
    echo
    log_warning "此操作将停止RAGFlow服务并导出所有数据"
    read -p "是否继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
    
    # 执行导出
    stop_ragflow
    create_export_dir
    export_images
    export_volumes
    copy_config
    create_import_script
    create_readme
    create_package
    
    log_success "RAGFlow导出完成！"
    echo
    log_info "迁移包: $PACKAGE_NAME"
    log_info "请将此文件传输到目标服务器并运行导入脚本"
}

# 运行主函数
main "$@"
