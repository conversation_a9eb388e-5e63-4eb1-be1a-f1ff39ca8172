# RAGFlow Docker 快速迁移指南

## 🚀 一键导出（源服务器）

```bash
# 1. 进入RAGFlow docker目录
cd /path/to/ragflow/docker

# 2. 下载并运行导出脚本
curl -O https://raw.githubusercontent.com/infiniflow/ragflow/main/docs/export-ragflow.sh
chmod +x export-ragflow.sh
./export-ragflow.sh
```

## 📦 传输迁移包

```bash
# 方法1: 网络传输（如果目标服务器可达）
scp ragflow-migration-*.tar.gz user@target-server:/home/<USER>/

# 方法2: 物理传输
# 将生成的 ragflow-migration-*.tar.gz 文件复制到USB设备或移动硬盘
```

## 🎯 一键导入（目标服务器）

```bash
# 1. 安装Docker和Docker Compose（如果未安装）
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
sudo usermod -aG docker $USER
newgrp docker

# 2. 解压并导入
tar xzf ragflow-migration-*.tar.gz
cd ragflow-export-*/
./import.sh

# 3. 启动服务
cd config
docker-compose up -d

# 4. 验证服务
docker-compose ps
curl http://localhost:9380/v1/system/status
```

## ✅ 验证清单

- [ ] 所有容器状态为 "Up"
- [ ] 可以访问 http://your-server-ip:9380
- [ ] 原有数据和配置完整
- [ ] 日志无错误信息

## 🔧 常见问题

### 导出失败
```bash
# 检查磁盘空间
df -h

# 检查Docker权限
docker info

# 手动停止服务
docker-compose down
```

### 导入失败
```bash
# 检查镜像导入
docker images | grep ragflow

# 检查卷创建
docker volume ls

# 查看详细错误
docker-compose logs
```

### 服务启动失败
```bash
# 检查端口占用
netstat -tlnp | grep -E "(9380|3306|9000|6379)"

# 重新启动
docker-compose down
docker-compose up -d

# 查看日志
docker-compose logs -f ragflow
```

## 📋 完整操作流程

| 步骤 | 源服务器 | 目标服务器 |
|------|----------|------------|
| 1 | 运行导出脚本 | 安装Docker环境 |
| 2 | 生成迁移包 | 接收迁移包 |
| 3 | 传输文件 | 解压文件 |
| 4 | - | 运行导入脚本 |
| 5 | - | 启动服务 |
| 6 | - | 验证功能 |

## ⚠️ 注意事项

1. **数据安全**: 导出前确保数据完整性
2. **版本兼容**: 确保Docker版本兼容
3. **磁盘空间**: 预留足够空间（建议10GB+）
4. **网络配置**: 检查防火墙和端口设置
5. **权限问题**: 确保Docker权限正确

## 📞 技术支持

如遇问题，请查看详细文档：
- [完整迁移指南](docker-offline-migration.md)
- [RAGFlow官方文档](https://ragflow.io/docs)
- [GitHub Issues](https://github.com/infiniflow/ragflow/issues)

---

**预计时间**: 导出15-30分钟，导入10-20分钟（取决于数据量和硬件性能）
